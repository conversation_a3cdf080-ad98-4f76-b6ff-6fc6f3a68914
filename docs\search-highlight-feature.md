# 搜索高亮功能

## 功能概述

新增的搜索高亮功能可以在用户搜索地址后，在地图上清晰地标识搜索位置，包括：

1. **搜索标记** - 在搜索位置显示动画标记
2. **区域高亮** - 如果搜索结果包含ZIP码，会高亮对应的ZIP区域
3. **自动清除** - 新搜索或清空搜索时自动清除之前的高亮

## 功能特性

### 🎯 搜索标记
- 在搜索位置显示📍图标
- 带有脉冲动画效果，更容易识别
- 橙色主题，与地图其他元素区分明显

### 🗺️ 区域高亮
- 自动检测搜索结果中的ZIP码
- 高亮对应的ZIP区域边界
- 使用橙色填充和边框，透明度适中

### 🔄 智能清除
- 新搜索时自动清除之前的高亮
- 点击搜索框清除按钮时清除高亮
- 清空搜索输入时自动清除高亮

## 技术实现

### 核心组件

1. **EventHandler** - 负责搜索高亮的核心逻辑
   - `highlightSearchLocation()` - 创建搜索标记和区域高亮
   - `clearSearchHighlight()` - 清除所有高亮效果

2. **MapContainer** - 监听搜索高亮事件
   - 监听 `searchHighlight` 事件
   - 监听 `clearSearchHighlight` 事件

3. **useDaycareMap** - 搜索逻辑增强
   - 提取搜索结果中的ZIP码
   - 触发搜索高亮事件

### 事件系统

```typescript
// 触发搜索高亮
window.dispatchEvent(
  new CustomEvent("searchHighlight", {
    detail: { 
      coordinates: [lng, lat], 
      zipCode: "90210",
      placeName: "Beverly Hills, CA" 
    },
  })
);

// 清除搜索高亮
window.dispatchEvent(new CustomEvent("clearSearchHighlight"));
```

### 样式配置

搜索标记使用CSS动画：
- 脉冲动画：2秒循环，从1倍缩放到2倍
- 颜色：橙色主题 (#ff6b35)
- 阴影：增强视觉效果

## 使用方法

1. **搜索地址**
   - 在搜索框中输入地址
   - 从下拉列表中选择结果
   - 地图会飞行到该位置并显示高亮

2. **查看高亮**
   - 搜索标记会在目标位置显示
   - 如果是ZIP码区域，会高亮整个区域

3. **清除高亮**
   - 进行新搜索
   - 点击搜索框的清除按钮
   - 清空搜索输入

## 兼容性

- ✅ 支持所有现代浏览器
- ✅ 响应式设计，适配移动设备
- ✅ 主题适配（明暗主题）
- ✅ 与现有地图功能完全兼容

## 性能优化

- 使用事件系统，避免组件间直接耦合
- 动画使用CSS，性能优异
- 智能清除机制，避免内存泄漏
- 图层复用，减少重复创建
